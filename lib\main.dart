/// Blue Booth Manager - 행사 부스 판매 매니징 하이브리드 앱
///
/// 주요 기능:
/// - 상품 관리 (등록, 수정, 삭제)
/// - 판매 관리 (판매 등록, 이력 조회)
/// - 선입금 관리 (등록, 수령상태 변경, 상세내역 조회)
/// - 서비스 상품권 관리
/// - 통계 및 분석
/// - 오프라인 지원 및 동기화
///
/// 기술 스택:
/// - Flutter 3.8+ (Material Design 3 Expressive 2025)
/// - Riverpod 2.6.1 (상태 관리)
/// - SQLite (로컬 데이터베이스)
/// - Equatable (상태 비교 최적화)
/// - 고주사율 디스플레이 최적화 (120Hz+ 지원)
/// - 네트워크 이미지 캐싱 최적화
///
/// 작성자: Blue
/// 버전: 1.0.0
/// 최종 업데이트: 2025년 7월
library;

import 'dart:async';
import 'dart:io';

import 'package:flutter/services.dart';

import 'package:flutter/material.dart';

import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_localizations/flutter_localizations.dart';
import 'package:syncfusion_localizations/syncfusion_localizations.dart';
import 'package:intl/date_symbol_data_local.dart';
import 'package:firebase_core/firebase_core.dart';
import 'package:lucide_icons/lucide_icons.dart';
import 'utils/app_colors.dart';

// import 'package:firebase_app_check/firebase_app_check.dart';
// import 'utils/app_check_config.dart';
// import 'utils/app_check_utils.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:firebase_analytics/firebase_analytics.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:flutter_phoenix/flutter_phoenix.dart';

import 'providers/product_provider.dart';
import 'providers/seller_provider.dart';
import 'providers/prepayment_provider.dart';
import 'providers/settings_provider.dart';
import 'providers/data_sync_provider.dart';
import 'providers/realtime_sync_provider.dart';
import 'services/differential_sync_service.dart';
import 'services/database_service.dart';
import 'screens/splash/splash_screen.dart';
import 'screens/inventory/inventory_screen.dart';
import 'screens/product/register_product_screen.dart';
import 'screens/prepayment/register_prepayment_screen.dart';
import 'screens/sale/sale_screen.dart';
import 'screens/sales_log/sales_log_screen.dart';
import 'screens/statistics/statistics_screen.dart';
import 'screens/settings/settings_screen.dart';
import 'screens/checklist/checklist_screen.dart';
import 'utils/logger_utils.dart';


import 'utils/mobile_performance_utils.dart';
import 'utils/state_sync_manager.dart';
import 'utils/image_cache.dart';
import 'utils/database_optimizer.dart';
import 'utils/batch_processor.dart';
import 'utils/object_pool.dart';
import 'utils/memory_manager.dart';

import 'utils/device_utils.dart';


import 'widgets/confirmation_dialog.dart';
import 'screens/onboarding/onboarding_screen.dart';
import 'screens/auth/login_screen.dart';
import 'screens/auth/register_screen.dart';
import 'screens/auth/nickname_screen.dart';
import 'screens/sync/sync_confirmation_screen.dart';
import 'providers/nickname_provider.dart';
import 'screens/settings/my_page_screen.dart';
import 'screens/home/<USER>';
import 'utils/safe_dialog_utils.dart';

import 'providers/unified_workspace_provider.dart';
import 'screens/onboarding/event_workspace_onboarding_screen.dart';
import 'screens/records_and_statistics/records_and_statistics_screen.dart';
import 'services/realtime_sync_service_main.dart';
import 'utils/network_status.dart';
import 'utils/common_utils.dart';

/// 앱의 진입점
void main() async {
  WidgetsFlutterBinding.ensureInitialized();

  // 시스템 UI 설정 - 안드로이드 네비게이션 바 이슈 해결 및 렌더링 최적화
  SystemChrome.setSystemUIOverlayStyle(
    const SystemUiOverlayStyle(
      statusBarColor: Colors.transparent,
      statusBarBrightness: Brightness.light,
      statusBarIconBrightness: Brightness.dark,
      systemNavigationBarColor: Colors.white, // 아이디어스처럼 하얀색 네비게이션 바
      systemNavigationBarDividerColor: Colors.transparent,
      systemNavigationBarIconBrightness: Brightness.dark, // 어두운 아이콘 (하얀 배경에 맞춤)
    ),
  );

  // Edge-to-edge 표시 설정 (cancelDraw 로그 최적화)
  SystemChrome.setEnabledSystemUIMode(
    SystemUiMode.edgeToEdge,
    overlays: [SystemUiOverlay.top, SystemUiOverlay.bottom], // 하단 네비게이션 바도 표시
  );

  // 화면 방향 설정 (렌더링 안정성 향상)
  SystemChrome.setPreferredOrientations([
    DeviceOrientation.portraitUp,
    DeviceOrientation.portraitDown,
    DeviceOrientation.landscapeLeft,
    DeviceOrientation.landscapeRight,
  ]);

  // 이미지 캐시 최적화 초기화
  ImageCacheManager.initialize();
  ImageCacheManager.schedulePeriodicOptimization();

  // 렌더링 최적화 초기화 (일시적으로 비활성화 - cancelDraw 무한반복 문제 해결)
  // RenderingOptimizer.initialize();
  // RenderingOptimizer.schedulePeriodicOptimization();

  // Firebase 초기화 (cancelDraw 무한 로그 문제 해결)
  try {
    // Firebase 기본 앱 초기화 (기본 앱으로 초기화 - App Check가 제대로 동작하도록)
    await Firebase.initializeApp();
    LoggerUtils.logInfo('Firebase 초기화 완료 (기본 앱)', tag: 'Main');

    // Firebase App Check 초기화 (임시 비활성화 - iOS JSON 오류 해결)
    try {
      LoggerUtils.logInfo('Firebase App Check 임시 비활성화', tag: 'Main');
      // App Check 관련 초기화 완전 비활성화
    } catch (e) {
      LoggerUtils.logError('Firebase App Check 초기화 건너뜀', tag: 'Main', error: e);
    }

    // Firebase Analytics 로그 최적화 (개발 중에는 비활성화)
    try {
      await FirebaseAnalytics.instance.setAnalyticsCollectionEnabled(false);
      LoggerUtils.logInfo('Firebase Analytics 비활성화 완료', tag: 'Main');
    } catch (e) {
      LoggerUtils.logError('Firebase Analytics 설정 실패', tag: 'Main', error: e);
    }
  } catch (e) {
    LoggerUtils.logError('Firebase 초기화 실패', tag: 'Main', error: e);
    // Firebase 초기화 실패해도 앱은 계속 실행
  }

  await initializeDateFormatting('ko_KR', null);
  runApp(
    Phoenix(
      child: ProviderScope(
        child: const AppEntryPoint(),
      ),
    ),
  );
}

/// 앱 진입점: Splash(Firebase 초기화) → 온보딩/로그인 플로우
class AppEntryPoint extends StatefulWidget {
  const AppEntryPoint({super.key});

  @override
  State<AppEntryPoint> createState() => _AppEntryPointState();
}

class _AppEntryPointState extends State<AppEntryPoint> {
  bool _isOnboarded = false;
  bool _isLoading = true;
  String _authMode = 'login'; // 'login' or 'register'
  bool _needsSyncCheck = false; // 동기화 확인이 필요한지 여부
  DateTime? _splashStartTime; // 스플래시 화면 시작 시간
  bool _skipAccountValidation = false; // 로그인 성공 직후 검증 건너뛰기

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
  }

  @override
  void initState() {
    super.initState();
    _splashStartTime = DateTime.now(); // 스플래시 시작 시간 기록
    // SplashScreen 완료 후 온보딩 체크 수행
    _checkOnboardingInBackground();
  }

  /// 백그라운드에서 온보딩 상태 체크 (UI 변경 없이 결과만 저장)
  Future<void> _checkOnboardingInBackground() async {
    try {
      await _checkOnboarding();
    } catch (e) {
      // 오류 발생 시 기본값 설정
      _isOnboarded = false;
      _authMode = 'login';
    }
  }

  /// 백그라운드에서 계정 유효성 검증 (UI 블로킹 없음)
  void _validateUserAccountInBackground() {
    _validateUserAccount().then((isValid) {
      if (!isValid && mounted) {
        // 계정이 유효하지 않으면 로그인 화면으로 이동
        setState(() {
          _isOnboarded = false;
          _authMode = 'login';
        });
      }
    }).catchError((error) {
      LoggerUtils.logError('백그라운드 계정 검증 오류', tag: 'AppEntryPoint', error: error);
      // 오류 발생 시에는 현재 상태 유지
    });
  }

  /// 스플래시 화면 완료 후 호출되는 콜백
  void _onSplashComplete() {
    _finishSplashScreen(
      isOnboarded: _isOnboarded,
      authMode: _authMode,
      needsSyncCheck: _needsSyncCheck,
    );
  }

  /// 스플래시 화면 최소 시간 보장 후 로딩 완료 처리
  Future<void> _finishSplashScreen({
    bool? isOnboarded,
    String? authMode,
    bool? needsSyncCheck,
  }) async {
    const minSplashDuration = Duration(seconds: 2); // 최소 2초 스플래시 표시

    if (_splashStartTime != null) {
      final elapsed = DateTime.now().difference(_splashStartTime!);
      final remainingTime = minSplashDuration - elapsed;

      if (remainingTime > Duration.zero) {
        // 최소 시간이 지나지 않았으면 추가 대기
        await Future.delayed(remainingTime);
      }
    }

    // 상태 업데이트
    setState(() {
      if (isOnboarded != null) _isOnboarded = isOnboarded;
      if (authMode != null) _authMode = authMode;
      if (needsSyncCheck != null) _needsSyncCheck = needsSyncCheck;
      _isLoading = false;
    });
  }



  Future<void> _checkOnboarding() async {
    final prefs = await SharedPreferences.getInstance();

    // 로그아웃 완료 플래그 확인
    final logoutCompleted = prefs.getBool('logout_completed') ?? false;
    if (logoutCompleted) {
      await prefs.remove('logout_completed');
      LoggerUtils.logInfo('🔄 로그아웃 완료 - 로그인 화면으로 이동', tag: 'AppEntryPoint');
      // Firebase 인증 상태도 확실히 클리어
      await FirebaseAuth.instance.signOut();
      // 로그아웃 상태 저장 (UI 변경 없음)
      _isOnboarded = false;
      _authMode = 'login';
      return;
    }

    // 앱 재시작 플래그 확인 (회원탈퇴 후)
    final forceAppRestart = prefs.getBool('force_app_restart') ?? false;
    if (forceAppRestart) {
      await prefs.remove('force_app_restart');
      LoggerUtils.logInfo('🔄 회원탈퇴 후 앱 재시작 - 온보딩 상태 초기화', tag: 'AppEntryPoint');
      // 온보딩 상태 저장 (UI 변경 없음)
      _isOnboarded = false;
      return;
    }

    // 계정 삭제 플래그 확인 및 알림 표시
    final accountDeletedByOtherDevice = prefs.getBool('account_deleted_by_other_device') ?? false;
    if (accountDeletedByOtherDevice) {
      await prefs.remove('account_deleted_by_other_device');
      WidgetsBinding.instance.addPostFrameCallback((_) {
        _showAccountDeletedDialog();
      });
    }

    // 디버그 모드에서의 상세한 상태 확인
    bool forceReset = false;
    bool debugModeReset = false;

    assert(() {
      // 디버그 모드에서만 실행되는 코드
      LoggerUtils.logInfo('🔍 디버그 모드: 온보딩 상태 상세 분석 시작', tag: 'AppEntryPoint');

      // 1. 기존 강제 리셋 플래그 확인
      forceReset = prefs.getBool('force_onboarding_reset') ?? false;
      if (forceReset) {
        LoggerUtils.logInfo('🔄 강제 온보딩 초기화 플래그 발견', tag: 'AppEntryPoint');
      }

      // 2. SharedPreferences 상태 상세 로깅
      final currentOnboardedState = prefs.getBool('isOnboarded');
      final allKeys = prefs.getKeys();
      LoggerUtils.logInfo('📋 SharedPreferences 상태:', tag: 'AppEntryPoint');
      LoggerUtils.logInfo('  - isOnboarded: $currentOnboardedState', tag: 'AppEntryPoint');
      LoggerUtils.logInfo('  - force_onboarding_reset: ${prefs.getBool('force_onboarding_reset')}', tag: 'AppEntryPoint');
      LoggerUtils.logInfo('  - 전체 키 개수: ${allKeys.length}', tag: 'AppEntryPoint');

      // 3. Firebase 사용자 상태 확인
      final user = FirebaseAuth.instance.currentUser;
      LoggerUtils.logInfo('🔐 Firebase 사용자 상태: ${user?.email ?? 'null'}', tag: 'AppEntryPoint');

      // 4. 디버그 모드에서의 추가 검증 로직 (이메일 인증 대기 상태 고려)
      if (currentOnboardedState == true && user == null) {
        // 이메일 인증 대기 상태인지 확인
        final isEmailVerificationPending = prefs.getBool('email_verification_pending') ?? false;

        if (isEmailVerificationPending) {
          LoggerUtils.logInfo('📧 이메일 인증 대기 중 - 온보딩 리셋하지 않음', tag: 'AppEntryPoint');
        } else {
          LoggerUtils.logInfo('⚠️  의심스러운 상태 감지: 온보딩 완료되었지만 Firebase 사용자 없음', tag: 'AppEntryPoint');
          LoggerUtils.logInfo('🔄 디버그 모드: 온보딩 상태 자동 리셋', tag: 'AppEntryPoint');
          debugModeReset = true;
        }
      }



      return true;
    }());

    // 리셋 실행
    if (forceReset || debugModeReset) {
      LoggerUtils.logInfo('🔄 온보딩 상태 리셋 실행', tag: 'AppEntryPoint');
      await prefs.remove('isOnboarded');
      await prefs.remove('force_onboarding_reset');
    }

    final isOnboarded = (forceReset || debugModeReset) ? false : (prefs.getBool('isOnboarded') ?? false);
    final user = FirebaseAuth.instance.currentUser;

    // 최종 상태 로깅
    LoggerUtils.logInfo('=== 온보딩 상태 확인 결과 ===', tag: 'AppEntryPoint');
    LoggerUtils.logInfo('isOnboarded: $isOnboarded', tag: 'AppEntryPoint');
    LoggerUtils.logInfo('Firebase User: ${user?.email ?? 'null'}', tag: 'AppEntryPoint');
    LoggerUtils.logInfo('Platform: ${Platform.isIOS ? 'iOS' : 'Android'}', tag: 'AppEntryPoint');
    LoggerUtils.logInfo('Force Reset: $forceReset', tag: 'AppEntryPoint');
    LoggerUtils.logInfo('Debug Mode Reset: $debugModeReset', tag: 'AppEntryPoint');
    LoggerUtils.logInfo('Final Decision: ${!isOnboarded ? "온보딩 화면 표시" : "로그인/메인 화면 표시"}', tag: 'AppEntryPoint');

    // 온보딩 상태 저장 (UI 변경 없음)
    _isOnboarded = isOnboarded;
  }

  void _completeOnboarding() async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setBool('isOnboarded', true);
    setState(() {
      _isOnboarded = true;
    });
  }

  /// 서버와 로컬 인증 상태 일치 여부 검증
  /// 로컬에 인증 정보가 있지만 서버에 계정이 삭제된 경우를 감지
  Future<bool> _validateUserAccount() async {
    final user = FirebaseAuth.instance.currentUser;
    if (user == null) return true; // 로그인되지 않은 상태는 정상

    try {
      LoggerUtils.logInfo('사용자 계정 유효성 검증 시작: ${user.email}', tag: 'AppEntryPoint');
      
      // ProviderScope.containerOf를 사용하여 DataSyncService에 접근
      final container = ProviderScope.containerOf(context, listen: false);
      final dataSyncService = container.read(dataSyncServiceProvider);
      
      final isValid = await dataSyncService.validateUserAccount();
      
      if (!isValid) {
        LoggerUtils.logWarning('서버에 계정이 존재하지 않음 - 로컬 인증 상태 클리어', tag: 'AppEntryPoint');
        
        // 로컬 인증 상태 클리어
        await FirebaseAuth.instance.signOut();
        
        // SharedPreferences 클리어 (온보딩 상태는 유지)
        final prefs = await SharedPreferences.getInstance();
        await prefs.remove('email_verification_pending');
        
        // 계정 삭제 알림 표시
        _showAccountDeletedByServerDialog();
        
        return false;
      }
      
      LoggerUtils.logInfo('사용자 계정 유효성 검증 완료: 정상', tag: 'AppEntryPoint');
      return true;
      
    } catch (e) {
      LoggerUtils.logError('사용자 계정 유효성 검증 실패', tag: 'AppEntryPoint', error: e);
      // 검증 실패 시에는 일시적 문제로 간주하여 계속 진행
      return true;
    } finally {
      // 계정 검증 완료
    }
  }

  /// 서버에서 계정이 삭제된 경우 알림 다이얼로그 표시
  void _showAccountDeletedByServerDialog() {
    if (!mounted) return;

    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => AlertDialog(
        title: const Text('계정을 찾을 수 없습니다'),
        content: const Text(
          '서버에서 계정 정보를 찾을 수 없습니다.\n'
          '계정이 삭제되었거나 일시적인 문제일 수 있습니다.\n'
          '로그인 화면으로 이동합니다.'
        ),
        actions: [
          ElevatedButton(
            onPressed: () {
              SafeDialogUtils.safePopDialog(context);
              // 로그인 화면으로 이동
              setState(() {
                _authMode = 'login';
              });
            },
            child: const Text('확인'),
          ),
        ],
      ),
    );
  }



  void _switchAuthMode() {
    setState(() {
      _authMode = _authMode == 'login' ? 'register' : 'login';
    });
  }

  /// 계정 삭제 알림 다이얼로그 표시
  void _showAccountDeletedDialog() {
    if (!mounted) return;

    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => AlertDialog(
        title: const Text('계정이 삭제되었습니다'),
        content: const Text(
          '다른 기기에서 회원탈퇴가 진행되어 계정이 삭제되었습니다.\n'
          '로컬 데이터가 모두 삭제되었으며, 로그인 화면으로 이동합니다.'
        ),
        actions: [
          ElevatedButton(
            onPressed: () {
              SafeDialogUtils.safePopDialog(context);
              // 온보딩 상태를 false로 설정하여 로그인 화면으로 이동
              setState(() {
                _isOnboarded = true; // 온보딩은 완료된 상태로 유지
              });
            },
            child: const Text('확인'),
          ),
        ],
      ),
    );
  }

  void _onLoginSuccess() async {
    // 로그인 성공 시 계정 검증 건너뛰기 설정
    setState(() {
      _skipAccountValidation = true;
    });
    
    // 로그인 성공 후 서버 데이터 확인 및 동기화 플래그 설정
    try {
      final container = ProviderScope.containerOf(context, listen: false);

      // SharedPreferences에서 로그인 시 확인한 서버 데이터 존재 여부 가져오기
      final prefs = await SharedPreferences.getInstance();
      final hasServerData = prefs.getBool('has_server_data_on_login') ?? false;

      LoggerUtils.logInfo('로그인 시 확인된 서버 데이터 존재 여부: $hasServerData', tag: 'AppEntryPoint');

      // 플래그 사용 후 제거 (일회성)
      await prefs.remove('has_server_data_on_login');

      // 서버 데이터가 있는 경우 워크스페이스 정보만 가볍게 로드 (전체 동기화는 SyncConfirmationScreen에서)
      if (hasServerData) {
        LoggerUtils.logInfo('서버 데이터 존재 - 워크스페이스 정보만 로드', tag: 'AppEntryPoint');
        try {
          // 워크스페이스 정보만 로드 (동기화는 SyncConfirmationScreen에서 수행)
          // refresh 메서드를 사용하되 데이터 동기화는 건너뛰도록 플래그 설정 필요
          // 현재는 기존 refresh 메서드 사용
          await container.read(unifiedWorkspaceProvider.notifier).refresh();
          LoggerUtils.logInfo('워크스페이스 정보 로드 완료', tag: 'AppEntryPoint');
        } catch (e) {
          LoggerUtils.logError('워크스페이스 정보 로드 실패', tag: 'AppEntryPoint', error: e);
        }
      } else {
        LoggerUtils.logInfo('서버 데이터 없음 - 동기화 건너뛰기', tag: 'AppEntryPoint');
      }

      // 실시간 동기화 서비스 준비 (RealtimeSyncService v2.0.0)
      try {
        container.read(realtimeSyncServiceProvider);
        LoggerUtils.logInfo('실시간 동기화 서비스 v2.0.0 준비 완료', tag: 'AppEntryPoint');
      } catch (e) {
        LoggerUtils.logError('실시간 동기화 서비스 접근 실패', tag: 'AppEntryPoint', error: e);
      }

      setState(() {
        _needsSyncCheck = hasServerData;
      });
    } catch (e) {
      LoggerUtils.logError('서버 데이터 확인 실패', tag: 'AppEntryPoint', error: e);
      setState(() {
        _needsSyncCheck = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    if (_isLoading) {
      return MaterialApp(
        debugShowCheckedModeBanner: false,
        home: SplashScreen(
          onInitializationComplete: _onSplashComplete,
        ),
      );
    }
    return MaterialApp(
      debugShowCheckedModeBanner: false,
      title: '파라바라',
      theme: _buildLightTheme(),
      darkTheme: _buildDarkTheme(),
      locale: const Locale('ko', 'KR'),
      localizationsDelegates: const [
        GlobalMaterialLocalizations.delegate,
        GlobalWidgetsLocalizations.delegate,
        GlobalCupertinoLocalizations.delegate,
        SfGlobalLocalizations.delegate,
      ],
      supportedLocales: const [
        Locale('ko', 'KR'),
        Locale('en', 'US'),
      ],
      routes: {
        '/my_page': (context) => const MyPageScreen(),
        '/inventory': (context) => const AppWrapper(child: InventoryScreen()),
        '/register_product': (context) => const AppWrapper(child: RegisterProductScreen()),
        '/register_prepayment': (context) => const AppWrapper(child: RegisterPrepaymentScreen()),
        '/sale': (context) => const AppWrapper(child: SaleScreen()),
        '/checklist': (context) => const ChecklistScreen(),
        '/sales_log': (context) => const AppWrapper(child: SalesLogScreen()),
        '/statistics': (context) => const AppWrapper(child: StatisticsScreen()),
        '/records_and_statistics': (context) => const AppWrapper(child: RecordsAndStatisticsScreen()),
        '/settings': (context) => const AppWrapper(child: SettingsScreen()),
      },
      home: _buildHome(),
    );
  }

  Widget _buildHome() {
    try {
      final user = FirebaseAuth.instance.currentUser;

      LoggerUtils.logInfo('_isOnboarded: $_isOnboarded', tag: 'AppEntryPoint');
      LoggerUtils.logInfo('user: ${user?.email ?? 'null'}', tag: 'AppEntryPoint');

      if (!_isOnboarded) {
        LoggerUtils.logInfo('→ OnboardingScreen으로 이동', tag: 'AppEntryPoint');
        return OnboardingScreen(onStart: _completeOnboarding);
      } else if (user == null) {
        LoggerUtils.logInfo('→ LoginScreen으로 이동', tag: 'AppEntryPoint');
        if (_authMode == 'login') {
          return LoginScreen(
            onLoginSuccess: _onLoginSuccess,
            onRegisterRequested: _switchAuthMode,
          );
        } else {
          return RegisterScreen(
            onRegisterSuccess: _switchAuthMode,
            onLoginRequested: _switchAuthMode,
          );
        }
      } else {
        // 로그인된 사용자가 있는 경우

        // 로그인 성공 직후이거나 이미 검증 완료된 경우 검증 건너뛰기
        if (_skipAccountValidation) {
          LoggerUtils.logInfo('로그인 성공 직후 - 계정 검증 건너뛰기', tag: 'AppEntryPoint');
          
          // 검증 건너뛰기 플래그 초기화 (다음 앱 시작 시를 위해)
          _skipAccountValidation = false;
          
          // 기존 로직 실행
          if (_needsSyncCheck) {
            return SyncConfirmationScreen(
              onSyncComplete: () {
                if (mounted) {
                  setState(() {
                    _needsSyncCheck = false;
                  });
                }
              },
            );
          } else {
            LoggerUtils.logInfo('→ AppWrapper로 이동', tag: 'AppEntryPoint');
            return const AppWrapper(child: InventoryScreen());
          }
        }
        
        // 계정 유효성 검증을 백그라운드에서 수행 (UI 블로킹 없음)
        _validateUserAccountInBackground();

        // 계정 검증 결과와 관계없이 바로 메인 화면으로 진행
        if (_needsSyncCheck) {
          // 서버에 데이터가 있어서 동기화 확인이 필요한 경우
          return SyncConfirmationScreen(
            onSyncComplete: () {
              if (mounted) {
                setState(() {
                  _needsSyncCheck = false;
                });
              }
            },
          );
        } else {
          // 일반적인 메인 화면
          LoggerUtils.logInfo('→ AppWrapper로 이동', tag: 'AppEntryPoint');
          return const AppWrapper(child: InventoryScreen());
        }
      }
    } catch (e) {
      // Firebase 관련 오류 시 온보딩 화면으로 이동
      LoggerUtils.logError('Firebase 인증 확인 실패', tag: 'AppEntryPoint', error: e);
      if (!_isOnboarded) {
        return OnboardingScreen(onStart: _completeOnboarding);
      } else {
        return const AppWrapper(child: InventoryScreen());
      }
    }
  }
}

// 기존 SplashScreen 클래스 제거됨 - 새로운 스플래시 스크린 사용



/// Material Design 3 Expressive 2025 라이트 테마
///
/// 2025년 트렌드를 반영한 현대적인 디자인:
/// - 적당한 곡률 (12-20px)
/// - 부드러운 그림자와 elevation
/// - 일관된 색상 체계
/// - 접근성을 고려한 타이포그래피
/// - Pretendard 폰트 적용
ThemeData _buildLightTheme() {
    return ThemeData(
      useMaterial3: true,
      colorScheme: AppColors.lightColorScheme,
      fontFamily: 'Pretendard', // 테스트용: 매우 눈에 띄는 변화

      // 현대적인 Typography 시스템 (2025 트렌드) - 모든 스타일에 폰트 적용
      textTheme: const TextTheme(
        displayLarge: TextStyle(
          fontSize: 57,
          fontWeight: FontWeight.w400,
          letterSpacing: -0.25,
          height: 1.12,
          fontFamily: 'Pretendard', // 테스트용
        ),
        displayMedium: TextStyle(
          fontSize: 45,
          fontWeight: FontWeight.w400,
          letterSpacing: 0,
          height: 1.16,
          fontFamily: 'Pretendard', // 테스트용
        ),
        displaySmall: TextStyle(
          fontSize: 36,
          fontWeight: FontWeight.w400,
          letterSpacing: 0,
          height: 1.22,
          fontFamily: 'Pretendard', // 테스트용
        ),
        headlineLarge: TextStyle(
          fontSize: 32,
          fontWeight: FontWeight.w400,
          letterSpacing: 0,
          height: 1.25,
          fontFamily: 'Pretendard', // 테스트용
        ),
        headlineMedium: TextStyle(
          fontSize: 28,
          fontWeight: FontWeight.w400,
          letterSpacing: 0,
          height: 1.29,
          fontFamily: 'Pretendard', // 테스트용
        ),
        headlineSmall: TextStyle(
          fontSize: 24,
          fontWeight: FontWeight.w400,
          letterSpacing: 0,
          height: 1.33,
          fontFamily: 'Pretendard', // 테스트용
        ),
        titleLarge: TextStyle(
          fontSize: 22,
          fontWeight: FontWeight.w500,
          letterSpacing: 0,
          height: 1.27,
          fontFamily: 'Pretendard', // 테스트용
        ),
        titleMedium: TextStyle(
          fontSize: 16,
          fontWeight: FontWeight.w500,
          letterSpacing: 0.15,
          height: 1.50,
          fontFamily: 'Pretendard', // 테스트용
        ),
        titleSmall: TextStyle(
          fontSize: 14,
          fontWeight: FontWeight.w500,
          letterSpacing: 0.1,
          height: 1.43,
          fontFamily: 'Pretendard', // 테스트용
        ),
        bodyLarge: TextStyle(
          fontSize: 16,
          fontWeight: FontWeight.w400,
          letterSpacing: 0.5,
          height: 1.50,
          fontFamily: 'Pretendard', // 테스트용
        ),
        bodyMedium: TextStyle(
          fontSize: 14,
          fontWeight: FontWeight.w400,
          letterSpacing: 0.25,
          height: 1.43,
          fontFamily: 'Pretendard', // 테스트용
        ),
        bodySmall: TextStyle(
          fontSize: 12,
          fontWeight: FontWeight.w400,
          letterSpacing: 0.4,
          height: 1.33,
          fontFamily: 'Pretendard', // 테스트용
        ),
        labelLarge: TextStyle(
          fontSize: 14,
          fontWeight: FontWeight.w500,
          letterSpacing: 0.1,
          height: 1.43,
          fontFamily: 'Pretendard', // 테스트용
        ),
        labelMedium: TextStyle(
          fontSize: 12,
          fontWeight: FontWeight.w500,
          letterSpacing: 0.5,
          height: 1.33,
          fontFamily: 'Pretendard', // 테스트용
        ),
        labelSmall: TextStyle(
          fontSize: 11,
          fontWeight: FontWeight.w500,
          letterSpacing: 0.5,
          height: 1.45,
          fontFamily: 'Pretendard', // 테스트용
        ),
      ),

      // 현대적인 AppBar 테마 (Material 3 Expressive)
      appBarTheme: AppBarTheme(
        centerTitle: false,
        elevation: 0,
        scrolledUnderElevation: 2,
        shadowColor: AppColors.elevation2,
        surfaceTintColor: AppColors.surfaceTint,
        backgroundColor: AppColors.primarySeed,
        foregroundColor: AppColors.onPrimary,
        titleTextStyle: const TextStyle(
          color: AppColors.onPrimary,
          fontSize: 22,
          fontWeight: FontWeight.w500,
          letterSpacing: 0,
        ),
        iconTheme: const IconThemeData(color: AppColors.onPrimary, size: 24),
        actionsIconTheme: const IconThemeData(
          color: AppColors.onPrimary,
          size: 24,
        ),
      ),

      // 현대적인 TabBar 테마
      tabBarTheme: TabBarThemeData(
        labelColor: AppColors.onPrimary,
        unselectedLabelColor: AppColors.onPrimary.withValues(alpha: 0.7),
        indicatorColor: AppColors.onPrimary,
        indicatorSize: TabBarIndicatorSize.tab,
        labelStyle: const TextStyle(
          fontWeight: FontWeight.w600,
          fontSize: 14,
          letterSpacing: 0.1,
        ),
        unselectedLabelStyle: const TextStyle(
          fontWeight: FontWeight.w500,
          fontSize: 14,
          letterSpacing: 0.1,
        ),
        overlayColor: WidgetStateProperty.all(
          AppColors.onPrimary.withValues(alpha: 0.12),
        ),
      ),

      // 현대적인 Card 테마 (Material 3 Expressive)
      cardTheme: CardThemeData(
        elevation: 1,
        shadowColor: AppColors.elevation1,
        surfaceTintColor: AppColors.surfaceTint,
        margin: EdgeInsets.zero,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(12),
        ),
        clipBehavior: Clip.antiAlias,
      ),

      // 현대적인 FloatingActionButton 테마
      floatingActionButtonTheme: FloatingActionButtonThemeData(
        backgroundColor: AppColors.primarySeed,
        foregroundColor: AppColors.onPrimary,
        elevation: 6,
        focusElevation: 8,
        hoverElevation: 8,
        highlightElevation: 12,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(16),
        ),
      ),

      // 현대적인 Button 테마들
      elevatedButtonTheme: ElevatedButtonThemeData(
        style: ElevatedButton.styleFrom(
          elevation: 1,
          shadowColor: AppColors.elevation1,
          surfaceTintColor: AppColors.surfaceTint,
          padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(20),
          ),
          textStyle: const TextStyle(
            fontSize: 14,
            fontWeight: FontWeight.w500,
            letterSpacing: 0.1,
          ),
        ),
      ),

      filledButtonTheme: FilledButtonThemeData(
        style: FilledButton.styleFrom(
          padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(20),
          ),
          textStyle: const TextStyle(
            fontSize: 14,
            fontWeight: FontWeight.w500,
            letterSpacing: 0.1,
          ),
        ),
      ),

      outlinedButtonTheme: OutlinedButtonThemeData(
        style: OutlinedButton.styleFrom(
          padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
          side: BorderSide(color: AppColors.neutral60),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(20),
          ),
          textStyle: const TextStyle(
            fontSize: 14,
            fontWeight: FontWeight.w500,
            letterSpacing: 0.1,
          ),
        ),
      ),

      textButtonTheme: TextButtonThemeData(
        style: TextButton.styleFrom(
          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(20),
          ),
          textStyle: const TextStyle(
            fontSize: 14,
            fontWeight: FontWeight.w500,
            letterSpacing: 0.1,
          ),
        ),
      ),

      // 현대적인 InputDecoration 테마
      inputDecorationTheme: InputDecorationTheme(
        filled: true,
        fillColor: AppColors.surfaceVariant,
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: BorderSide.none,
        ),
        enabledBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: BorderSide(color: AppColors.neutral30),
        ),
        focusedBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: BorderSide(color: AppColors.primarySeed, width: 2),
        ),
        errorBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: BorderSide(color: AppColors.error),
        ),
        contentPadding: const EdgeInsets.symmetric(
          horizontal: 16,
          vertical: 12,
        ),
        labelStyle: TextStyle(
          color: AppColors.onSurfaceVariant,
          fontSize: 14,
          fontWeight: FontWeight.w400,
        ),
        hintStyle: TextStyle(
          color: AppColors.onSurfaceVariant.withValues(alpha: 0.6),
          fontSize: 14,
          fontWeight: FontWeight.w400,
        ),
      ),

      // Drawer 테마
      drawerTheme: DrawerThemeData(
        backgroundColor: AppColors.surface,
        surfaceTintColor: AppColors.surfaceTint,
        elevation: 16,
        shadowColor: AppColors.elevation4,
        shape: const RoundedRectangleBorder(
          borderRadius: BorderRadius.only(
            topRight: Radius.circular(16),
            bottomRight: Radius.circular(16),
          ),
        ),
      ),

      // Divider 테마
      dividerTheme: DividerThemeData(
        color: AppColors.neutral30,
        thickness: 1,
        space: 1,
      ),

      // Chip 테마 (현대적 디자인)
      chipTheme: ChipThemeData(
        backgroundColor: AppColors.surfaceVariant,
        deleteIconColor: AppColors.onSurfaceVariant,
        disabledColor: AppColors.neutral20,
        selectedColor: AppColors.secondaryLight,
        secondarySelectedColor: AppColors.secondary,
        shadowColor: AppColors.elevation1,
        labelStyle: TextStyle(
          color: AppColors.onSurfaceVariant,
          fontSize: 14,
          fontWeight: FontWeight.w500,
        ),
        secondaryLabelStyle: TextStyle(
          color: AppColors.onSecondary,
          fontSize: 14,
          fontWeight: FontWeight.w500,
        ),
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
      ),

      // ProgressIndicator 테마 (Material 3 보라색 방지)
      progressIndicatorTheme: ProgressIndicatorThemeData(
        color: AppColors.primarySeed,
        linearTrackColor: AppColors.neutral30,
        circularTrackColor: AppColors.neutral30,
        refreshBackgroundColor: AppColors.surface,
      ),

      // Dialog 테마 (Material 3 보라색 방지)
      dialogTheme: DialogThemeData(
        backgroundColor: AppColors.surface,
        surfaceTintColor: AppColors.surfaceTint,
        elevation: 24,
        shadowColor: AppColors.elevation4,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(16),
        ),
        titleTextStyle: TextStyle(
          color: AppColors.onSurface,
          fontSize: 20,
          fontWeight: FontWeight.w600,
        ),
        contentTextStyle: TextStyle(
          color: AppColors.onSurfaceVariant,
          fontSize: 14,
          fontWeight: FontWeight.w400,
        ),
      ),

      // SnackBar 테마 (Material 3 보라색 방지)
      snackBarTheme: SnackBarThemeData(
        backgroundColor: AppColors.neutral80,
        contentTextStyle: TextStyle(
          color: AppColors.neutral0,
          fontSize: 14,
          fontWeight: FontWeight.w400,
        ),
        actionTextColor: AppColors.primarySeed,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(8),
        ),
        behavior: SnackBarBehavior.floating,
        elevation: 6,
      ),

      // BottomSheet 테마 (Material 3 보라색 방지)
      bottomSheetTheme: BottomSheetThemeData(
        backgroundColor: AppColors.surface,
        surfaceTintColor: AppColors.surfaceTint,
        elevation: 16,
        shadowColor: AppColors.elevation4,
        shape: const RoundedRectangleBorder(
          borderRadius: BorderRadius.only(
            topLeft: Radius.circular(16),
            topRight: Radius.circular(16),
          ),
        ),
        clipBehavior: Clip.antiAlias,
      ),

      // PopupMenu 테마 (Material 3 보라색 방지)
      popupMenuTheme: PopupMenuThemeData(
        color: AppColors.surface,
        surfaceTintColor: AppColors.surfaceTint,
        elevation: 8,
        shadowColor: AppColors.elevation3,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(12),
        ),
        textStyle: TextStyle(
          color: AppColors.onSurface,
          fontSize: 14,
          fontWeight: FontWeight.w400,
        ),
      ),

      // Switch 테마 (Material 3 보라색 방지)
      switchTheme: SwitchThemeData(
        thumbColor: WidgetStateProperty.resolveWith((states) {
          if (states.contains(WidgetState.selected)) {
            return AppColors.primarySeed;
          }
          return AppColors.neutral60;
        }),
        trackColor: WidgetStateProperty.resolveWith((states) {
          if (states.contains(WidgetState.selected)) {
            return AppColors.primarySeed.withValues(alpha: 0.5);
          }
          return AppColors.neutral30;
        }),
      ),

      // Checkbox 테마 (Material 3 보라색 방지)
      checkboxTheme: CheckboxThemeData(
        fillColor: WidgetStateProperty.resolveWith((states) {
          if (states.contains(WidgetState.selected)) {
            return AppColors.primarySeed;
          }
          return Colors.transparent;
        }),
        checkColor: WidgetStateProperty.all(AppColors.onPrimary),
        side: BorderSide(color: AppColors.neutral60, width: 2),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(4),
        ),
      ),

      // Radio 테마 (Material 3 보라색 방지)
      radioTheme: RadioThemeData(
        fillColor: WidgetStateProperty.resolveWith((states) {
          if (states.contains(WidgetState.selected)) {
            return AppColors.primarySeed;
          }
          return AppColors.neutral60;
        }),
      ),
    );
  }

/// Material Design 3 Expressive 2025 다크 테마
///
/// 라이트 테마와 동일한 구조를 유지하되,
/// colorScheme에서 자동으로 다크 색상들이 적용됩니다.
/// Pretendard 폰트 적용
ThemeData _buildDarkTheme() {
    return ThemeData(
      useMaterial3: true,
      colorScheme: AppColors.darkColorScheme,
      fontFamily: 'Pretendard', // 전역 폰트 설정

      // 완전한 텍스트 테마 정의 (모든 TextStyle에 명시적 fontFamily 설정)
      textTheme: const TextTheme(
        displayLarge: TextStyle(fontFamily: 'Pretendard'),
        displayMedium: TextStyle(fontFamily: 'Pretendard'),
        displaySmall: TextStyle(fontFamily: 'Pretendard'),
        headlineLarge: TextStyle(fontFamily: 'Pretendard'),
        headlineMedium: TextStyle(fontFamily: 'Pretendard'),
        headlineSmall: TextStyle(fontFamily: 'Pretendard'),
        titleLarge: TextStyle(fontFamily: 'Pretendard'),
        titleMedium: TextStyle(fontFamily: 'Pretendard'),
        titleSmall: TextStyle(fontFamily: 'Pretendard'),
        bodyLarge: TextStyle(fontFamily: 'Pretendard'),
        bodyMedium: TextStyle(fontFamily: 'Pretendard'),
        bodySmall: TextStyle(fontFamily: 'Pretendard'),
        labelLarge: TextStyle(fontFamily: 'Pretendard'),
        labelMedium: TextStyle(fontFamily: 'Pretendard'),
        labelSmall: TextStyle(fontFamily: 'Pretendard'),
      ),

      // 다크 테마 AppBar
      appBarTheme: AppBarTheme(
        centerTitle: false,
        elevation: 0,
        scrolledUnderElevation: 2,
        shadowColor: AppColors.elevation2,
        surfaceTintColor: AppColors.primaryLight,
        backgroundColor: AppColors.primaryDark,
        foregroundColor: AppColors.onPrimary,
        titleTextStyle: const TextStyle(
          color: AppColors.onPrimary,
          fontSize: 22,
          fontWeight: FontWeight.w500,
          letterSpacing: 0,
          fontFamily: 'Pretendard',
        ),
        iconTheme: const IconThemeData(color: AppColors.onPrimary, size: 24),
        actionsIconTheme: const IconThemeData(
          color: AppColors.onPrimary,
          size: 24,
        ),
      ),

      // 다크 테마 TabBar
      tabBarTheme: TabBarThemeData(
        labelColor: AppColors.onPrimary,
        unselectedLabelColor: AppColors.onPrimary.withValues(alpha: 0.7),
        indicatorColor: AppColors.onPrimary,
        indicatorSize: TabBarIndicatorSize.tab,
        labelStyle: const TextStyle(
          fontWeight: FontWeight.w600,
          fontSize: 14,
          letterSpacing: 0.1,
        ),
        unselectedLabelStyle: const TextStyle(
          fontWeight: FontWeight.w500,
          fontSize: 14,
          letterSpacing: 0.1,
        ),
        overlayColor: WidgetStateProperty.all(
          AppColors.onPrimary.withValues(alpha: 0.12),
        ),
      ),

      // 다크 테마 Card
      cardTheme: CardThemeData(
        elevation: 1,
        shadowColor: AppColors.elevation1,
        surfaceTintColor: AppColors.primaryLight,
        margin: EdgeInsets.zero,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(12),
        ),
        clipBehavior: Clip.antiAlias,
      ),

      // 다크 테마 FloatingActionButton
      floatingActionButtonTheme: FloatingActionButtonThemeData(
        backgroundColor: AppColors.primaryDark,
        foregroundColor: AppColors.onPrimary,
        elevation: 6,
        focusElevation: 8,
        hoverElevation: 8,
        highlightElevation: 12,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(16),
        ),
      ),

      // 다크 테마 Button들
      elevatedButtonTheme: ElevatedButtonThemeData(
        style: ElevatedButton.styleFrom(
          elevation: 1,
          shadowColor: AppColors.elevation1,
          surfaceTintColor: AppColors.primaryLight,
          padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(20),
          ),
          textStyle: const TextStyle(
            fontSize: 14,
            fontWeight: FontWeight.w500,
            letterSpacing: 0.1,
          ),
        ),
      ),

      filledButtonTheme: FilledButtonThemeData(
        style: FilledButton.styleFrom(
          padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(20),
          ),
          textStyle: const TextStyle(
            fontSize: 14,
            fontWeight: FontWeight.w500,
            letterSpacing: 0.1,
          ),
        ),
      ),

      outlinedButtonTheme: OutlinedButtonThemeData(
        style: OutlinedButton.styleFrom(
          padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
          side: BorderSide(color: AppColors.neutral60),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(20),
          ),
          textStyle: const TextStyle(
            fontSize: 14,
            fontWeight: FontWeight.w500,
            letterSpacing: 0.1,
          ),
        ),
      ),

      textButtonTheme: TextButtonThemeData(
        style: TextButton.styleFrom(
          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(20),
          ),
          textStyle: const TextStyle(
            fontSize: 14,
            fontWeight: FontWeight.w500,
            letterSpacing: 0.1,
          ),
        ),
      ),

      // 다크 테마 InputDecoration
      inputDecorationTheme: InputDecorationTheme(
        filled: true,
        fillColor: AppColors.surfaceVariant,
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: BorderSide.none,
        ),
        enabledBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: BorderSide(color: AppColors.neutral30),
        ),
        focusedBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: BorderSide(color: AppColors.primaryDark, width: 2),
        ),
        errorBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: BorderSide(color: AppColors.error),
        ),
        contentPadding: const EdgeInsets.symmetric(
          horizontal: 16,
          vertical: 12,
        ),
        labelStyle: TextStyle(
          color: AppColors.onSurfaceVariant,
          fontSize: 14,
          fontWeight: FontWeight.w400,
        ),
        hintStyle: TextStyle(
          color: AppColors.onSurfaceVariant.withValues(alpha: 0.6),
          fontSize: 14,
          fontWeight: FontWeight.w400,
        ),
      ),

      // 다크 테마 Drawer
      drawerTheme: DrawerThemeData(
        backgroundColor: AppColors.surface,
        surfaceTintColor: AppColors.primaryLight,
        elevation: 16,
        shadowColor: AppColors.elevation4,
        shape: const RoundedRectangleBorder(
          borderRadius: BorderRadius.only(
            topRight: Radius.circular(16),
            bottomRight: Radius.circular(16),
          ),
        ),
      ),

      // 다크 테마 Divider
      dividerTheme: DividerThemeData(
        color: AppColors.neutral30,
        thickness: 1,
        space: 1,
      ),

      // 다크 테마 Chip
      chipTheme: ChipThemeData(
        backgroundColor: AppColors.surfaceVariant,
        deleteIconColor: AppColors.onSurfaceVariant,
        disabledColor: AppColors.neutral20,
        selectedColor: AppColors.secondaryLight,
        secondarySelectedColor: AppColors.secondary,
        shadowColor: AppColors.elevation1,
        labelStyle: TextStyle(
          color: AppColors.onSurfaceVariant,
          fontSize: 14,
          fontWeight: FontWeight.w500,
        ),
        secondaryLabelStyle: TextStyle(
          color: AppColors.onSecondary,
          fontSize: 14,
          fontWeight: FontWeight.w500,
        ),
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
      ),
    );
  }

/// 앱 래퍼 위젯
class AppWrapper extends ConsumerStatefulWidget {
  final Widget child;

  const AppWrapper({
    super.key,
    required this.child,
  });

  @override
  ConsumerState<AppWrapper> createState() => _AppWrapperState();
}

class _AppWrapperState extends ConsumerState<AppWrapper>
    with WidgetsBindingObserver {
  static const String _tag = 'AppWrapper';

  int _currentTabIndex = 0; // 하단 탭 인덱스


  @override
  void initState() {
    super.initState();
    LoggerUtils.methodStart('initState', tag: _tag);
    WidgetsBinding.instance.addObserver(this);
    // 백그라운드에서 초기화 수행 (UI 블로킹 없음)
    _initializeAppDataInBackground();
    LoggerUtils.methodEnd('initState', tag: _tag);
  }

  /// 백그라운드에서 앱 데이터 초기화 (UI 블로킹 없음)
  void _initializeAppDataInBackground() async {
    try {
      LoggerUtils.logInfo('백그라운드 앱 초기화 시작', tag: _tag);

      // 1단계: 기기 타입 감지 및 설정
      await _detectAndSetDeviceType();

      // 2단계: 필수 데이터 로드 (스플래시에서 이미 로드된 것 제외)
      await ref.read(settingsNotifierProvider.notifier).loadSettings();
      // 닉네임은 스플래시에서 이미 로드됨 - 중복 로딩 제거

      // 실시간 동기화 서비스 초기화
      try {
        final realtimeSyncService = RealtimeSyncService();
        if (!realtimeSyncService.isInitialized.value) {
          await realtimeSyncService.initialize();
        }
        LoggerUtils.logInfo('실시간 동기화 서비스 초기화 완료', tag: _tag);
      } catch (e) {
        LoggerUtils.logError('실시간 동기화 서비스 초기화 실패', tag: _tag, error: e);
      }

      // 백그라운드에서 나머지 데이터 로드
      _loadDataInBackground();

      LoggerUtils.logInfo('백그라운드 앱 초기화 완료', tag: _tag);
    } catch (e) {
      LoggerUtils.logError('백그라운드 앱 초기화 실패', tag: _tag, error: e);
    }
  }

  /// 백그라운드에서 나머지 데이터를 로드하는 메서드
  void _loadDataInBackground() {
    // UI가 표시된 후 백그라운드에서 실행
    WidgetsBinding.instance.addPostFrameCallback((_) async {
      try {
        LoggerUtils.logInfo('백그라운드 데이터 로딩 시작', tag: _tag);

        // Firebase 사용자 로그인 상태 확인 및 스마트 동기화 수행
        final user = FirebaseAuth.instance.currentUser;
        if (user != null) {
          await _performDataSync();
        }

        // 행사 워크스페이스별 데이터 로드
        final workspaceState = ref.read(workspaceProvider);
        if (workspaceState.hasCurrentWorkspace) {
          await _loadWorkspaceData();
        }

        LoggerUtils.logInfo('백그라운드 데이터 로딩 완료', tag: _tag);
      } catch (e) {
        LoggerUtils.logError('백그라운드 데이터 로딩 실패', tag: _tag, error: e);
      }
    });
  }

  /// Firebase 데이터 동기화 수행 (SyncConfirmationScreen을 거치지 않은 경우에만)
  Future<void> _performDataSync() async {
    try {
      // SyncConfirmationScreen을 거쳤다면 이미 동기화가 완료되었으므로 건너뛰기
      final prefs = await SharedPreferences.getInstance();
      final syncCompleted = prefs.getBool('sync_completed_recently') ?? false;

      if (syncCompleted) {
        LoggerUtils.logInfo('최근 동기화 완료됨 - AppWrapper 동기화 건너뛰기', tag: 'AppWrapper');
        // 플래그 제거 (일회성)
        await prefs.remove('sync_completed_recently');
        return;
      }

      // 새 디바이스 감지 (로컬 데이터가 거의 없는 경우)
      final isNewDevice = await _detectNewDevice();

      if (isNewDevice) {
        LoggerUtils.logInfo('새 디바이스 감지 - 전체 다운로드 우선 실행', tag: 'AppWrapper');
        await _performFullDownloadForNewDevice();
        return;
      }

      LoggerUtils.logInfo('앱 시작 시 차분 동기화 시작', tag: 'AppWrapper');

      // 차분 동기화 서비스 생성
      final databaseService = ref.read(databaseServiceProvider);
      final diffSyncService = DifferentialSyncService(databaseService);

      // 1단계: 행사 목록 동기화 (메타데이터만)
      LoggerUtils.logInfo('1단계: 행사 목록 동기화 시작', tag: 'AppWrapper');
      final eventsResult = await diffSyncService.syncEventsList(
        onProgress: (message) {
          LoggerUtils.logInfo('행사 목록 동기화: $message', tag: 'AppWrapper');
        },
        onError: (error) {
          LoggerUtils.logError('행사 목록 동기화 오류: $error', tag: 'AppWrapper');
        }
      );

      LoggerUtils.logInfo(
        '행사 목록 동기화 완료 - 다운로드: ${eventsResult.downloaded}, 삭제: ${eventsResult.deleted}',
        tag: 'AppWrapper'
      );

      // 2단계: 현재 워크스페이스 확인 (행사 목록 변경으로 인해 삭제되었을 수 있음)
      final workspaceState = ref.read(workspaceProvider);
      if (!workspaceState.hasCurrentWorkspace) {
        LoggerUtils.logInfo('현재 워크스페이스 없음 - 현재 행사 데이터 동기화 건너뛰기', tag: 'AppWrapper');
        return;
      }

      final currentEventId = workspaceState.currentWorkspace!.id;
      LoggerUtils.logInfo('2단계: 현재 행사 데이터 동기화 시작 (ID: $currentEventId)', tag: 'AppWrapper');

      // 3단계: 현재 행사의 데이터만 차분 동기화
      final result = await diffSyncService.syncCurrentEventData(
        currentEventId,
        onProgress: (message) {
          LoggerUtils.logInfo('현재 행사 동기화: $message', tag: 'AppWrapper');
        },
        onError: (error) {
          LoggerUtils.logError('현재 행사 동기화 오류: $error', tag: 'AppWrapper');
        }
      );

      LoggerUtils.logInfo(
        '현재 행사 동기화 완료 - 다운로드: ${result.downloaded}, 업로드: ${result.uploaded}, 삭제: ${result.deleted}, 스킵: ${result.skipped}',
        tag: 'AppWrapper'
      );

      if (result.hasErrors) {
        LoggerUtils.logWarning('동기화 중 오류 발생: ${result.errors.join(', ')}', tag: 'AppWrapper');
      }

      LoggerUtils.logInfo('차분 동기화 완료 - 실시간 동기화가 자동으로 활성화됨', tag: 'AppWrapper');
    } catch (e) {
      LoggerUtils.logError('앱 시작 시 데이터 동기화 실패', tag: 'AppWrapper', error: e);
      // 동기화 실패해도 앱은 계속 실행
    }
  }

  /// 워크스페이스 데이터 로드
  Future<void> _loadWorkspaceData() async {
    try {
      final productNotifier = ref.read(productNotifierProvider.notifier);
      final prepaymentNotifier = ref.read(prepaymentNotifierProvider.notifier);

      // 에러 상태 클리어
      productNotifier.clearError();
      prepaymentNotifier.clearError();

      // 데이터베이스 락 방지를 위해 순차적으로 로드
      await productNotifier.loadProducts();
      await prepaymentNotifier.loadPrepayments(showLoading: false);
      await ref.read(sellerNotifierProvider.notifier).loadSellers();
    } catch (e) {
      LoggerUtils.logError('워크스페이스 데이터 로드 실패', tag: _tag, error: e);
    }
  }

  /// 기기 타입 감지 및 Provider에 설정
  Future<void> _detectAndSetDeviceType() async {
    try {
      if (!mounted) return;

      // DeviceUtils를 사용한 기기 타입 감지 (가장 신뢰할 만함)
      final isTablet = DeviceUtils.isTablet(context);

      // 기기 정보 로깅
      final mediaQuery = MediaQuery.of(context);
      final size = mediaQuery.size;

      LoggerUtils.logInfo('=== 기기 타입 감지 결과 ===', tag: _tag);
      LoggerUtils.logInfo('화면 크기: ${size.width.toStringAsFixed(1)} x ${size.height.toStringAsFixed(1)}dp', tag: _tag);
      LoggerUtils.logInfo('최소 변: ${size.shortestSide.toStringAsFixed(1)}dp', tag: _tag);
      LoggerUtils.logInfo('기기 타입: ${isTablet ? "태블릿" : "스마트폰"}', tag: _tag);

      // Provider에 기기 타입 설정
      await ref.read(settingsNotifierProvider.notifier).setDeviceType(isTablet);

    } catch (e, stackTrace) {
      LoggerUtils.logError('기기 타입 감지 실패', tag: _tag, error: e, stackTrace: stackTrace);
      // 감지 실패 시 스마트폰으로 기본 설정
      await ref.read(settingsNotifierProvider.notifier).setDeviceType(false);
    }
  }







  @override
  void dispose() {
    LoggerUtils.methodStart('dispose', tag: _tag);
    MobilePerformanceUtils.stopMemoryMonitoring();
    MobilePerformanceUtils.performMemoryCleanup();
    _cleanupOptimizationSystems();
    WidgetsBinding.instance.removeObserver(this);
    LoggerUtils.methodEnd('dispose', tag: _tag);
    super.dispose();
  }

  /// 새 디바이스 감지 (로컬 데이터가 거의 없는 경우)
  Future<bool> _detectNewDevice() async {
    try {
      final workspaceState = ref.read(workspaceProvider);
      final hasWorkspaces = workspaceState.workspaces.isNotEmpty;

      if (!hasWorkspaces) {
        LoggerUtils.logInfo('워크스페이스가 없음 - 새 디바이스로 판단', tag: _tag);
        return true;
      }

      // 추가 조건: 서버 데이터 존재 여부 확인
      final dataSyncService = ref.read(dataSyncServiceProvider);
      final hasServerData = await dataSyncService.hasServerData();

      if (hasServerData && workspaceState.workspaces.length <= 1) {
        LoggerUtils.logInfo('서버 데이터 존재하지만 로컬 워크스페이스 적음 - 새 디바이스로 판단', tag: _tag);
        return true;
      }

      return false;
    } catch (e) {
      LoggerUtils.logError('새 디바이스 감지 실패', tag: _tag, error: e);
      return false;
    }
  }

  /// 새 디바이스를 위한 전체 다운로드 수행
  Future<void> _performFullDownloadForNewDevice() async {
    try {
      final dataSyncService = ref.read(dataSyncServiceProvider);

      LoggerUtils.logInfo('새 디바이스 전체 다운로드 시작', tag: _tag);

      await dataSyncService.performBidirectionalSync(
        onProgress: (message) {
          LoggerUtils.logInfo('새 디바이스 동기화: $message', tag: _tag);
        },
        onError: (error) {
          LoggerUtils.logError('새 디바이스 동기화 오류: $error', tag: _tag);
        },
      );

      LoggerUtils.logInfo('새 디바이스 전체 다운로드 완료', tag: _tag);

      // 워크스페이스 Provider 새로고침
      await ref.read(unifiedWorkspaceProvider.notifier).refresh();

    } catch (e) {
      LoggerUtils.logError('새 디바이스 전체 다운로드 실패', tag: _tag, error: e);
      // 실패해도 차분 동기화는 계속 진행
    }
  }

  void _cleanupOptimizationSystems() {
    try {
      StateSyncManager().clearAllStates();
      LoggerUtils.logInfo('최적화 시스템 정리 완료', tag: _tag);
    } catch (e) {
      LoggerUtils.logError('최적화 시스템 정리 실패', tag: _tag, error: e);
    }
  }

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    LoggerUtils.methodStart('didChangeAppLifecycleState', tag: _tag, data: {'state': state.name});
    if (state == AppLifecycleState.resumed) {
      WidgetsBinding.instance.addPostFrameCallback((_) {
        if (mounted) {
          _refreshProvidersIfNeeded();
        }
      });
    } else if (state == AppLifecycleState.detached || state == AppLifecycleState.paused) {
      // 앱 종료 시 메모리 누수 방지를 위한 정리
      try {
        NetworkStatusUtil.dispose();
        CommonUtils.disposeDebounceTimer();
        LoggerUtils.logInfo('앱 종료 시 메모리 정리 완료', tag: _tag);
      } catch (e) {
        LoggerUtils.logError('앱 종료 시 메모리 정리 실패', tag: _tag, error: e);
      }
    }
    LoggerUtils.methodEnd('didChangeAppLifecycleState', tag: _tag);
  }

  Future<void> _refreshProvidersIfNeeded() async {
    if (!mounted) return;
    LoggerUtils.methodStart('_refreshProvidersIfNeeded', tag: _tag);
    try {
      // 실시간 동기화가 활성화되어 있으므로 수동 새로고침은 불필요
      LoggerUtils.logInfo('실시간 동기화 활성화로 인해 수동 새로고침 생략', tag: _tag);
    } catch (e, stackTrace) {
      LoggerUtils.error('Provider 상태 새로고침 실패', tag: _tag, error: e, stackTrace: stackTrace, data: {'operation': 'refresh_providers_if_needed'});
    }
    LoggerUtils.methodEnd('_refreshProvidersIfNeeded', tag: _tag);
  }



  @override
  Widget build(BuildContext context) {
    return AnnotatedRegion<SystemUiOverlayStyle>(
      value: const SystemUiOverlayStyle(
        statusBarColor: Colors.transparent,
        statusBarBrightness: Brightness.light,
        statusBarIconBrightness: Brightness.dark,
        systemNavigationBarColor: Colors.white, // 아이디어스처럼 하얀색
        systemNavigationBarDividerColor: Colors.transparent,
        systemNavigationBarIconBrightness: Brightness.dark,
      ),
      child: PopScope(
      canPop: false,
      onPopInvokedWithResult: (didPop, result) async {
        if (didPop) return;

        final shouldExit = await ConfirmationDialog.show(
          context: context,
          title: '앱 종료',
          message: '앱을 종료하시겠습니까?',
          confirmLabel: '예',
          cancelLabel: '아니오',
        );

        if (shouldExit == true && context.mounted) {
          if (Theme.of(context).platform == TargetPlatform.android ||
              Theme.of(context).platform == TargetPlatform.iOS) {
            SystemNavigator.pop();
          } else {
            // 다른 플랫폼 (Web, Desktop 등)
            Navigator.of(context).popUntil((route) => route.isFirst);
          }
        }
      },
      child: Consumer(
        builder: (context, ref, _) {
          final nickname = ref.watch(nicknameProvider);

          if (nickname == null) {
            LoggerUtils.logInfo('→ NicknameScreen으로 이동', tag: 'AppWrapper');
            return NicknameScreen(
              onNicknameSet: () {
                setState(() {}); // 닉네임 등록 후 화면 갱신
              },
            );
          }

          return _buildWorkspaceCheck();
        },
      ),
    ),
    );
  }

  /// 워크스페이스 확인 로직
  Widget _buildWorkspaceCheck() {
    // 닉네임이 있으면 행사 워크스페이스 존재 확인
    final workspaceState = ref.watch(unifiedWorkspaceProvider);

    if (!workspaceState.hasWorkspaces) {
      // 행사 워크스페이스가 없으면 행사 워크스페이스 생성 온보딩 화면으로 이동
      return EventWorkspaceOnboardingScreen(
        onWorkspaceCreated: () {
          LoggerUtils.logInfo('행사 워크스페이스 생성 완료 - UI 갱신', tag: _tag);
          if (mounted) {
            setState(() {});
          }
        },
      );
    }

    // 행사 워크스페이스는 있지만 현재 행사 워크스페이스가 설정되지 않은 경우
    if (!workspaceState.hasCurrentWorkspace) {
      // 로딩 화면 표시
      return const Scaffold(
        body: Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              CircularProgressIndicator(),
              SizedBox(height: 16),
              Text('행사 워크스페이스 정보를 불러오는 중...'),
            ],
          ),
        ),
      );
    }

    // 정상적으로 현재 행사 워크스페이스가 설정된 경우 하단 탭 네비게이션 표시
    return _buildMainScreenWithBottomTabs();
  }

  /// 하단 탭 네비게이션이 있는 메인 화면 빌드
  Widget _buildMainScreenWithBottomTabs() {
    return Scaffold(
      resizeToAvoidBottomInset: false, // 키보드나 토스트 때문에 FAB가 올라가지 않도록
      body: _buildCurrentTabContent(),
      bottomNavigationBar: _buildBottomNavigationBar(),
      floatingActionButton: _buildFloatingPOSButton(),
      floatingActionButtonLocation: FloatingActionButtonLocation.centerDocked,
    );
  }

  /// 현재 선택된 탭의 콘텐츠 빌드 (IndexedStack 사용으로 성능 최적화)
  Widget _buildCurrentTabContent() {
    return IndexedStack(
      index: _currentTabIndex,
      children: [
        _buildHomeTab(),        // 0: 홈
        widget.child,           // 1: 재고관리 (기존 InventoryScreen 사용)
        Container(),            // 2: POS (사용하지 않음 - FloatingActionButton으로 직접 이동)
        const RecordsAndStatisticsScreen(), // 3: 기록&통계
        _buildMyTab(),          // 4: MY
      ],
    );
  }

  /// 하단 네비게이션 바 빌드 (11번가 스타일 - 직선 테두리 + 중앙 FAB)
  Widget _buildBottomNavigationBar() {
    return Container(
      decoration: const BoxDecoration(
        border: Border(
          top: BorderSide(
            color: Color(0xFFE0E0E0), // 원래 11번가 스타일 구분선
            width: 1.0,
          ),
        ),
      ),
      child: BottomAppBar(
        shape: const CircularNotchedRectangle(), // 중앙에 노치 생성
        notchMargin: 8.0, // 노치 여백
        color: Colors.white, // 원래 깔끔한 하얀색 배경
        elevation: 0.0, // Container에서 구분선 처리하므로 elevation 제거
        child: SizedBox(
          height: 60,
          child: Row(
            children: [
              Expanded(child: _buildNavItem(0, LucideIcons.home, '홈')),
              Expanded(child: _buildNavItem(1, LucideIcons.creditCard, '선입금')),
              Expanded(
                child: AnimatedContainer(
                  duration: const Duration(milliseconds: 200),
                  curve: Curves.easeInOut,
                  margin: const EdgeInsets.symmetric(horizontal: 4),
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(12),
                    gradient: _currentTabIndex == 2 ? RadialGradient(
                      center: Alignment.center,
                      radius: 1.2,
                      colors: [
                        AppColors.primarySeed.withValues(alpha: 0.15), // 중앙 진함
                        AppColors.primarySeed.withValues(alpha: 0.08), // 중간
                        AppColors.primarySeed.withValues(alpha: 0.03), // 가장자리 연함
                        Colors.transparent, // 완전 투명
                      ],
                      stops: const [0.0, 0.4, 0.7, 1.0],
                    ) : null,
                  ),
                  child: Material(
                    color: Colors.transparent,
                    child: InkWell(
                      onTap: () {
                        // POS 버튼과 동일하게 SaleScreen으로 이동
                        Navigator.of(context).push(
                          MaterialPageRoute(
                            builder: (context) => const SaleScreen(),
                          ),
                        );
                      },
                      borderRadius: BorderRadius.circular(12),
                      splashColor: AppColors.primarySeed.withValues(alpha: 0.25), // 더 진한 ripple
                      highlightColor: AppColors.primarySeed.withValues(alpha: 0.12), // 더 진한 highlight
                      child: Container(
                        width: double.infinity,
                        height: double.infinity,
                        child: Column(
                          mainAxisSize: MainAxisSize.min,
                          mainAxisAlignment: MainAxisAlignment.end,
                          children: [
                            const SizedBox(height: 32), // FloatingActionButton 공간 확보
                            Text(
                              'POS',
                              style: TextStyle(
                                color: const Color(0xFF9E9E9E), // 항상 비활성 색상으로 표시
                                fontSize: 12,
                                fontWeight: FontWeight.w400,
                              ),
                              textAlign: TextAlign.center,
                            ),
                            const SizedBox(height: 4), // 하단 여백
                          ],
                        ),
                      ),
                    ),
                  ),
                ),
              ), // 중앙 FAB 공간 + POS 텍스트
              Expanded(child: _buildNavItem(3, LucideIcons.fileBarChart, '기록&통계')),
              Expanded(child: _buildNavItem(4, LucideIcons.user, 'MY')),
            ],
          ),
        ),
      ),
    );
  }

  /// 네비게이션 아이템 빌드 (중앙 그라데이션 확산 효과)
  Widget _buildNavItem(int index, IconData icon, String label) {
    final isSelected = _currentTabIndex == index;
    final color = isSelected
        ? AppColors.primarySeed // 선택된 탭은 앱바와 같은 웜 테라코타
        : const Color(0xFF9E9E9E); // 비선택 탭은 원래 회색

    return AnimatedContainer(
      duration: const Duration(milliseconds: 200),
      curve: Curves.easeInOut,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(12),
        gradient: isSelected ? RadialGradient(
          center: Alignment.center,
          radius: 1.2,
          colors: [
            AppColors.primarySeed.withValues(alpha: 0.15), // 중앙 진함
            AppColors.primarySeed.withValues(alpha: 0.08), // 중간
            AppColors.primarySeed.withValues(alpha: 0.03), // 가장자리 연함
            Colors.transparent, // 완전 투명
          ],
          stops: const [0.0, 0.4, 0.7, 1.0],
        ) : null,
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: () => _onTabTapped(index),
          borderRadius: BorderRadius.circular(12),
          splashColor: AppColors.primarySeed.withValues(alpha: 0.25), // 더 진한 ripple
          highlightColor: AppColors.primarySeed.withValues(alpha: 0.12), // 더 진한 highlight
          child: Container(
            width: double.infinity,
            height: double.infinity,
            child: Column(
              mainAxisSize: MainAxisSize.min,
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(
                  icon,
                  color: color,
                  size: 24,
                ),
                const SizedBox(height: 4),
                Text(
                  label,
                  style: TextStyle(
                    color: color,
                    fontSize: 12,
                    fontWeight: isSelected ? FontWeight.w600 : FontWeight.w400,
                  ),
                  textAlign: TextAlign.center,
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  /// 중앙 POS 플로팅 버튼 (앱바와 같은 웜 테라코타)
  Widget _buildFloatingPOSButton() {
    return Transform.translate(
      offset: const Offset(0, 8), // 버튼을 아래로 8px 이동
      child: FloatingActionButton(
        onPressed: () {
          // 새로운 페이지로 이동 (하단 앱바 숨김)
          Navigator.of(context).push(
            MaterialPageRoute(
              builder: (context) => const SaleScreen(),
            ),
          );
        },
        backgroundColor: AppColors.primarySeed, // 앱바와 같은 웜 테라코타
        foregroundColor: Colors.white, // 흰색 아이콘
        elevation: 8.0,
        child: const Icon(
          LucideIcons.shoppingCart,
          size: 28,
        ),
      ),
    );
  }





  /// 탭 선택 처리
  void _onTabTapped(int index) {
    if (_currentTabIndex != index) {
      setState(() {
        _currentTabIndex = index;
      });
      LoggerUtils.logInfo('탭 변경: $index', tag: _tag);
    }
  }

  /// 홈 탭 화면 - 대시보드
  Widget _buildHomeTab() {
    return Scaffold(
      appBar: AppBar(
        title: const Text('리더보드'),
        centerTitle: true,
        backgroundColor: AppColors.primarySeed,
        foregroundColor: Colors.white,
        surfaceTintColor: Colors.transparent, // Material 3에서 색상 덮어쓰기 방지
        elevation: 0,
        automaticallyImplyLeading: false, // 뒤로가기 버튼 제거
      ),
      body: const HomeDashboardScreen(),
    );
  }





  /// MY 탭 화면 - 마이페이지로 연결
  Widget _buildMyTab() {
    return const MyPageScreen();
  }
}

/// 앱 종료 시 메모리 누수 방지를 위한 통합 정리 함수
///
/// 중위험 메모리 누수 항목들의 정적 리소스를 모두 정리합니다.
/// 앱의 생명주기 종료 시점에 호출되어야 합니다.
Future<void> cleanupAppResources() async {
  try {
    // Utils 클래스들의 정적 리소스 정리
    MobilePerformanceUtils.shutdown();
    DatabaseOptimizer.shutdown();
    await ImageCacheManager.clearAllCache(); // 기존 메서드 사용
    LoggerUtils.shutdown();
    BatchProcessor.shutdown();
    ObjectPoolManager.shutdown();
    MemoryManager.shutdown();

    // Services 클래스들의 정적 리소스 정리
    RealtimeSyncService.shutdown();

    LoggerUtils.logInfo('앱 리소스 정리 완료');
  } catch (e) {
    // 정리 중 오류가 발생해도 앱 종료는 계속 진행
    LoggerUtils.logError('앱 리소스 정리 중 오류 발생', error: e);
  }
}




