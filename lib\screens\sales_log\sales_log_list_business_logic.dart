import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../models/sales_log.dart';
import '../../models/sales_log_display_item.dart';
import '../../models/transaction_type.dart';
import '../../providers/sales_log_provider.dart';
import '../../utils/toast_utils.dart';
import '../../widgets/confirmation_dialog.dart';
import '../../utils/app_colors.dart';

import 'sales_log_list_filter_logic.dart';
import 'sales_log_list_ui_components.dart';


/// 판매 기록 목록의 비즈니스 로직을 담당하는 클래스
///
/// 주요 기능:
/// - 판매 기록 삭제 (재고 복구 포함)
/// - 그룹 판매 기록 삭제
/// - 그룹 상세 다이얼로그 관리
/// - 필터링된 데이터 관리
class SalesLogListBusinessLogic {
  /// 필터링된 표시 아이템 목록 생성
  ///
  /// [ref]: Riverpod ref
  /// [selectedSeller]: 선택된 판매자
  /// [selectedTransactionType]: 선택된 거래 유형
  /// [selectedDateRange]: 선택된 날짜 범위
  /// 반환값: 필터링된 표시 아이템 목록
  static List<SalesLogDisplayItem> getFilteredDisplayItems({
    required WidgetRef ref,
    required String selectedSeller,
    required TransactionType? selectedTransactionType,
    required DateTimeRange? selectedDateRange,
  }) {
    final allDisplayItems = ref.watch(salesLogDisplayItemsProvider);
    
    return SalesLogListFilterLogic.getFilteredDisplayItems(
      allDisplayItems: allDisplayItems,
      selectedSeller: selectedSeller,
      selectedTransactionType: selectedTransactionType,
      selectedDateRange: selectedDateRange,
    );
  }

  /// 판매 기록 삭제 (재고 복구 포함)
  ///
  /// [ref]: Riverpod ref
  /// [context]: BuildContext
  /// [salesLog]: 삭제할 판매 기록
  /// 반환값: 삭제 성공 여부
  static Future<bool> deleteSalesLogComplete({
    required WidgetRef ref,
    required BuildContext context,
    required SalesLog salesLog,
  }) async {
    try {
      // 삭제 확인 다이얼로그 표시
      final shouldDelete = await SalesLogListUiComponents.showDeleteConfirmDialog(
        context,
        salesLog,
      );

      if (!shouldDelete) {
        return false;
      }

      // 완전한 판매 기록 삭제 (재고 복구 + Firebase 동기화 포함)
      final result = await ref
          .read(salesLogNotifierProvider.notifier)
          .deleteSalesLogComplete(salesLog);
    
      // 단순화된 데이터 갱신
      await ref.read(salesLogNotifierProvider.notifier).loadSalesLogs();

      // 결과 메시지 표시
      if (context.mounted) {
        ToastUtils.showToast(
          context,
          result,
          duration: ToastUtils.shortDuration,
        );
      }

      return true;
    } catch (e) {
      // 오류 메시지 표시
      if (context.mounted) {
        ToastUtils.showError(
          context,
          '삭제 중 오류가 발생했습니다: $e',
        );
      }
      return false;
    }
  }

  /// 그룹 판매 기록 삭제 (재고 복구 포함)
  ///
  /// [ref]: Riverpod ref
  /// [context]: BuildContext
  /// [groupedSale]: 삭제할 그룹 판매 기록
  /// 반환값: 삭제 성공 여부
  static Future<bool> deleteGroupSalesLogWithStockRestore({
    required WidgetRef ref,
    required BuildContext context,
    required GroupedSale groupedSale,
  }) async {
    try {
      // 삭제 확인 다이얼로그 표시
      final shouldDelete = await SalesLogListUiComponents.showDeleteGroupConfirmDialog(
        context,
        groupedSale,
      );

      if (!shouldDelete) {
        return false;
      }

      // 그룹 판매 기록 삭제 및 재고 복구 (SalesLogCrud에서 처리)
      final result = await ref
          .read(salesLogNotifierProvider.notifier)
          .deleteGroupedSaleAndUpdateStock(groupedSale);

      // 단순화된 데이터 갱신
      await ref.read(salesLogNotifierProvider.notifier).loadSalesLogs();

      // 결과 메시지 표시
      if (context.mounted) {
        ToastUtils.showToast(
          context,
          result,
          duration: ToastUtils.shortDuration,
        );
      }

      return true;
    } catch (e) {
      // 오류 메시지 표시
      if (context.mounted) {
        ToastUtils.showError(
          context,
          '그룹 삭제 중 오류가 발생했습니다: $e',
        );
      }
      return false;
    }
  }

  /// 그룹 상세 다이얼로그 표시
  ///
  /// [context]: BuildContext
  /// [groupedSale]: 표시할 그룹 판매 기록
  /// [selectedSeller]: 선택된 판매자 (필터링용)
  /// [onItemDelete]: 개별 아이템 삭제 콜백
  /// [productCategoryMap]: 상품ID -> 카테고리명 매핑 (선택사항)
  static void showGroupDetailDialog({
    required BuildContext context,
    required GroupedSale groupedSale,
    required String selectedSeller,
    required Function(SalesLog) onItemDelete,
    Map<int, String>? productCategoryMap,
  }) {
    // 모든 상품을 표시하되, 판매자 필터가 적용된 경우 해당 판매자 상품을 위로 정렬
    List<SalesLog> itemsToShow = List<SalesLog>.from(groupedSale.items);

    // 판매자 필터가 적용된 경우 해당 판매자 상품을 위로 정렬
    if (selectedSeller != '전체 판매자') {
      itemsToShow.sort((a, b) {
        final aIsCurrentSeller =
            (a.sellerName ?? '알 수 없음') == selectedSeller;
        final bIsCurrentSeller =
            (b.sellerName ?? '알 수 없음') == selectedSeller;

        // 해당 판매자 상품을 우선으로 정렬
        if (aIsCurrentSeller && !bIsCurrentSeller) return -1;
        if (!aIsCurrentSeller && bIsCurrentSeller) return 1;

        // 같은 우선순위 내에서는 상품명 순으로 정렬
        return a.productName.compareTo(b.productName);
      });
    }

    final currentGroupedItems = List<SalesLog>.from(itemsToShow);

    showDialog(
      context: context,
      builder: (context) {
        return StatefulBuilder(
          builder: (context, setState) {
            // 현재 표시 중인 아이템들의 총계 재계산
            int currentTotalQuantity = currentGroupedItems.fold<int>(
              0,
              (sum, item) => sum + item.soldQuantity,
            );
            int currentTotalAmount = currentGroupedItems.fold<int>(
              0,
              (sum, item) => sum + item.totalAmount,
            );



            return Dialog(
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(16.0),
              ),
              backgroundColor: AppColors.surface,
              elevation: 8,
              child: Container(
                constraints: const BoxConstraints(maxWidth: 600, maxHeight: 600),
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    // 모던 헤더
                    Container(
                      padding: const EdgeInsets.all(24.0),
                      decoration: BoxDecoration(
                        gradient: LinearGradient(
                          begin: Alignment.topLeft,
                          end: Alignment.bottomRight,
                          colors: [
                            AppColors.primarySeed,
                            AppColors.primarySeed.withValues(alpha: 0.8),
                          ],
                        ),
                        borderRadius: const BorderRadius.only(
                          topLeft: Radius.circular(16),
                          topRight: Radius.circular(16),
                        ),
                      ),
                      child: Row(
                        children: [
                          Container(
                            padding: const EdgeInsets.all(12),
                            decoration: BoxDecoration(
                              color: AppColors.onPrimary.withValues(alpha: 0.2),
                              borderRadius: BorderRadius.circular(12),
                            ),
                            child: Icon(
                              Icons.group,
                              color: AppColors.onPrimary,
                              size: 24,
                            ),
                          ),
                          const SizedBox(width: 16),
                          Expanded(
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Text(
                                  '다중판매 상세',
                                  style: TextStyle(
                                    fontFamily: 'Pretendard',
                                    fontSize: 20,
                                    fontWeight: FontWeight.bold,
                                    color: AppColors.onPrimary,
                                  ),
                                ),
                                const SizedBox(height: 4),
                                Row(
                                  children: [
                                    Text(
                                      '총 $currentTotalQuantity개 • ${_formatCurrency(currentTotalAmount)}',
                                      style: TextStyle(
                                        fontFamily: 'Pretendard',
                                        fontSize: 14,
                                        fontWeight: FontWeight.w500,
                                        color: AppColors.onPrimary.withValues(alpha: 0.9),
                                      ),
                                    ),
                                    // 세트 할인 정보 표시
                                    if (groupedSale.hasSetDiscount) ...[
                                      const SizedBox(width: 8),
                                      Container(
                                        padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                                        decoration: BoxDecoration(
                                          color: AppColors.onPrimary.withValues(alpha: 0.2),
                                          borderRadius: BorderRadius.circular(4),
                                        ),
                                        child: Row(
                                          mainAxisSize: MainAxisSize.min,
                                          children: [
                                            Icon(
                                              Icons.local_offer,
                                              size: 12,
                                              color: AppColors.onPrimary,
                                            ),
                                            const SizedBox(width: 4),
                                            Text(
                                              '세트 -${_formatCurrency(groupedSale.totalSetDiscountAmount)}',
                                              style: TextStyle(
                                                fontFamily: 'Pretendard',
                                                fontSize: 11,
                                                color: AppColors.onPrimary,
                                                fontWeight: FontWeight.w500,
                                              ),
                                            ),
                                          ],
                                        ),
                                      ),
                                    ],
                                  ],
                                ),
                              ],
                            ),
                          ),
                          IconButton(
                            onPressed: () => Navigator.of(context).pop(),
                            icon: Icon(Icons.close, color: AppColors.onPrimary),
                            style: IconButton.styleFrom(
                              backgroundColor: AppColors.onPrimary.withValues(alpha: 0.2),
                              shape: RoundedRectangleBorder(
                                borderRadius: BorderRadius.circular(12),
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),
                    // 모던 컨텐츠
                    Expanded(
                      child: Container(
                        padding: const EdgeInsets.fromLTRB(16, 8, 16, 16),
                        child: ListView.builder(
                          itemCount: currentGroupedItems.length,
                          itemBuilder: (context, index) {
                            final item = currentGroupedItems[index];
                            // 현재 판매자 필터에 해당하는지 확인
                            final isCurrentSeller =
                                selectedSeller == '전체 판매자' ||
                                (item.sellerName ?? '알 수 없음') == selectedSeller;

                            return Container(
                              margin: const EdgeInsets.only(bottom: 8),
                              child: Card(
                                elevation: isCurrentSeller ? 1 : 0.5,
                                shadowColor: AppColors.elevation1,
                                surfaceTintColor: AppColors.surfaceTint,
                                shape: RoundedRectangleBorder(
                                  borderRadius: BorderRadius.circular(12),
                                  side: isCurrentSeller
                                    ? BorderSide.none
                                    : BorderSide(
                                        color: AppColors.neutral30.withValues(alpha: 0.5),
                                        width: 1,
                                      ),
                                ),
                                child: Opacity(
                                  opacity: isCurrentSeller ? 1.0 : 0.6,
                                  child: Padding(
                                    padding: const EdgeInsets.all(16),
                                    child: Row(
                                      children: [
                                        // 상품 정보
                                        Expanded(
                                          child: Column(
                                            crossAxisAlignment: CrossAxisAlignment.start,
                                            children: [
                                              // 상품명
                                              Text(
                                                _buildProductDisplayName(item, productCategoryMap),
                                                style: TextStyle(
                                                  fontFamily: 'Pretendard',
                                                  fontSize: 15,
                                                  fontWeight: FontWeight.w600,
                                                  color: isCurrentSeller
                                                      ? AppColors.onSurface
                                                      : AppColors.onSurfaceVariant,
                                                ),
                                                maxLines: 2,
                                                overflow: TextOverflow.ellipsis,
                                              ),
                                              const SizedBox(height: 4),
                                              // 판매자 및 거래 유형
                                              Text(
                                                '${item.transactionType.displayName} • ${item.sellerName ?? '알 수 없음'}',
                                                style: TextStyle(
                                                  fontFamily: 'Pretendard',
                                                  fontSize: 13,
                                                  color: isCurrentSeller
                                                      ? AppColors.onSurfaceVariant
                                                      : AppColors.neutral50,
                                                ),
                                              ),
                                              // 할인 정보 (세트 할인 또는 수동 할인)
                                              if (item.setDiscountAmount > 0 || item.manualDiscountAmount > 0)
                                                Padding(
                                                  padding: const EdgeInsets.only(top: 4),
                                                  child: Container(
                                                    padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                                                    decoration: BoxDecoration(
                                                      color: isCurrentSeller
                                                        ? AppColors.success.withValues(alpha: 0.1)
                                                        : AppColors.neutral20,
                                                      borderRadius: BorderRadius.circular(6),
                                                    ),
                                                    child: Row(
                                                      mainAxisSize: MainAxisSize.min,
                                                      children: [
                                                        Icon(
                                                          item.setDiscountAmount > 0 ? Icons.local_offer : Icons.money_off,
                                                          size: 12,
                                                          color: isCurrentSeller
                                                              ? AppColors.success
                                                              : AppColors.neutral50,
                                                        ),
                                                        const SizedBox(width: 4),
                                                        Text(
                                                          item.setDiscountAmount > 0
                                                            ? '세트할인 -${_formatCurrency(item.setDiscountAmount)}'
                                                            : '수동할인 -${_formatCurrency(item.manualDiscountAmount)}',
                                                          style: TextStyle(
                                                            fontFamily: 'Pretendard',
                                                            fontSize: 11,
                                                            color: isCurrentSeller
                                                                ? AppColors.success
                                                                : AppColors.neutral50,
                                                            fontWeight: FontWeight.w500,
                                                          ),
                                                        ),
                                                      ],
                                                    ),
                                                  ),
                                                ),
                                            ],
                                          ),
                                        ),
                                        const SizedBox(width: 16),
                                        // 수량과 금액
                                        Column(
                                          crossAxisAlignment: CrossAxisAlignment.end,
                                          children: [
                                            Text(
                                              '${item.soldQuantity}개',
                                              style: TextStyle(
                                                fontFamily: 'Pretendard',
                                                fontSize: 13,
                                                fontWeight: FontWeight.w600,
                                                color: isCurrentSeller
                                                    ? AppColors.onSurfaceVariant
                                                    : AppColors.neutral50,
                                              ),
                                            ),
                                            const SizedBox(height: 2),
                                            Text(
                                              _formatCurrency(item.totalAmount),
                                              style: TextStyle(
                                                fontFamily: 'Pretendard',
                                                fontSize: 15,
                                                fontWeight: FontWeight.bold,
                                                color: isCurrentSeller
                                                    ? AppColors.success
                                                    : AppColors.neutral50,
                                              ),
                                            ),
                                          ],
                                        ),
                                        const SizedBox(width: 12),
                                        // 개별 삭제 버튼
                                        SizedBox(
                                          width: 32,
                                          height: 32,
                                          child: IconButton(
                                            onPressed: () async {
                                              // 개별 삭제 확인 다이얼로그
                                              final shouldDelete = await ConfirmationDialog.showDelete(
                                                context: context,
                                                title: '판매 기록 삭제',
                                                message: '\'${item.productName}\' 판매 기록을 삭제하시겠습니까? 해당 상품의 재고가 ${item.soldQuantity}만큼 복원됩니다.',
                                                confirmLabel: '삭제',
                                                cancelLabel: '취소',
                                              );

                                              if (shouldDelete == true) {
                                                // 개별 삭제 실행
                                                await onItemDelete(item);

                                                // 현재 그룹에서 삭제된 아이템 제거
                                                currentGroupedItems.removeAt(index);

                                                if (currentGroupedItems.isEmpty) {
                                                  // 그룹이 비어있으면 다이얼로그 닫기
                                                  if (context.mounted) {
                                                    Navigator.of(context).pop();
                                                  }
                                                } else {
                                                  // 그룹이 남아있으면 UI 업데이트
                                                  setState(() {});
                                                }

                                                // 토스트 메시지 표시
                                                if (context.mounted) {
                                                  ToastUtils.showToast(
                                                    context,
                                                    '판매 기록이 삭제되었습니다.',
                                                    duration: ToastUtils.shortDuration,
                                                  );
                                                }
                                              }
                                            },
                                            icon: Icon(
                                              Icons.delete_outline,
                                              size: 16,
                                              color: isCurrentSeller
                                                  ? AppColors.error
                                                  : AppColors.neutral50,
                                            ),
                                            padding: EdgeInsets.zero,
                                          ),
                                        ),
                                      ],
                                    ),
                                  ),
                                ),
                              ),
                            );
                          },
                        ),
                      ),
                    ),
                    // 모던 하단 버튼
                    Container(
                      padding: const EdgeInsets.all(20.0),
                      decoration: BoxDecoration(
                        color: AppColors.surface,
                        borderRadius: const BorderRadius.only(
                          bottomLeft: Radius.circular(16),
                          bottomRight: Radius.circular(16),
                        ),
                        border: Border(
                          top: BorderSide(
                            color: AppColors.neutral20,
                            width: 1,
                          ),
                        ),
                      ),
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.end,
                        children: [
                          TextButton(
                            onPressed: () => Navigator.of(context).pop(),
                            style: TextButton.styleFrom(
                              padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
                              backgroundColor: AppColors.surfaceVariant,
                              foregroundColor: AppColors.onSurfaceVariant,
                              shape: RoundedRectangleBorder(
                                borderRadius: BorderRadius.circular(8),
                              ),
                            ),
                            child: Text(
                              '닫기',
                              style: TextStyle(
                                fontFamily: 'Pretendard',
                                fontSize: 16,
                                fontWeight: FontWeight.w600,
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ),
            );
          },
        );
      },
    );
  }

  /// 통화 포맷팅 헬퍼 메서드
  static String _formatCurrency(int amount) {
    return '₩${amount.toStringAsFixed(0)}';
  }

  /// 상품명에 카테고리명을 포함하여 표시명 생성
  static String _buildProductDisplayName(SalesLog item, Map<int, String>? productCategoryMap) {
    // 카테고리 정보가 있고 productId가 있는 경우 카테고리명 포함
    if (productCategoryMap != null && item.productId != null) {
      final categoryName = productCategoryMap[item.productId];
      if (categoryName != null) {
        return '[$categoryName]${item.productName}';
      }
    }

    // 기본적으로 상품명만 반환
    return item.productName;
  }
}